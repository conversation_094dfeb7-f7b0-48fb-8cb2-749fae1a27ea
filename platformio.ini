; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:huidu_hd_wf1]
platform = https://github.com/Jason2866/platform-espressif32.git#Arduino/IDF53
board = huidu_hd_wf1
framework = arduino
monitor_speed = 115200
build_flags =
	-DCORE_DEBUG_LEVEL=5
	-DWF1=1 ; Custom define, WF1
	-DARDUINO_USB_CDC_ON_BOOT=0

lib_deps = 
	tanakamasayuki/I2C BM8563 RTC@^1.0.4
	adafruit/Adafruit GFX Library@^1.11.5
	adafruit/Adafruit BusIO@^1.14.1
	ayushsharma82/ElegantOTA@^3.1.2
	https://github.com/mrfaptastic/ESP32-HUB75-MatrixPanel-DMA
	fbiego/ESP32Time@^2.0.6
	thoma<PERSON><PERSON><PERSON><PERSON>/Bounce2@^2.72



[env:huidu_hd_wf2]
platform = https://github.com/Jason2866/platform-espressif32.git#Arduino/IDF53
board = huidu_hd_wf2
framework = arduino
monitor_speed = 115200
build_flags =
	-DCORE_DEBUG_LEVEL=5
	-DWF2=1 ; Custom define, WF2
	-DARDUINO_USB_CDC_ON_BOOT=0


lib_deps = 
	tanakamasayuki/I2C BM8563 RTC@^1.0.4
	adafruit/Adafruit GFX Library@^1.11.5
	adafruit/Adafruit BusIO@^1.14.1
	ayushsharma82/ElegantOTA@^3.1.2
	https://github.com/mrfaptastic/ESP32-HUB75-MatrixPanel-DMA 
	fbiego/ESP32Time@^2.0.6
	thomasfredericks/Bounce2@^2.72
