// Custom LED Matrix Firmware (leveraging the HUB75 DMA library) for the Huidu HUB75 Series Control Cards.
// Example shop link: https://www.aliexpress.com/item/1005005038544582.html -> WF1
// Example shop link: https://www.aliexpress.com/item/1005002271988988.html -> WF2

#if defined(WF1)
  #include "hd-wf1-esp32s2-config.h"
#elif defined(WF2)
  #include "hd-wf2-esp32s3-config.h"
#else
  #error "Please define either WF1 or WF2"
#endif  


#include <esp_err.h>
#include <esp_log.h>
#include "debug.h"
#include "littlefs_core.h"
#include <ctime>
#include "driver/ledc.h"

#include <Arduino.h>
#include <WiFi.h>
#include <WiFiMulti.h>
#include <HTTPClient.h>

#include <WebServer.h>
#include <ESPmDNS.h>
#include <I2C_BM8563.h>   // https://github.com/tanakamasayuki/I2C_BM8563

#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include <ElegantOTA.h> // upload firmware by going to http://<ipaddress>/update

#include <ESP32Time.h>
#include <Bounce2.h>

// Include USB CDC support for ESP32-S2/S3
#if SOC_USB_SERIAL_JTAG_SUPPORTED
#include "USB.h"
#include "HWCDC.h"
#endif

#define fs LittleFS

/*----------------------------- Pixel Remapping Functions -------------------------------*/
// Remap functions for 32x32 physical panel treated as 64x16 logical panel
// Based on ICN5020B chip configuration

int remap_x(int x1, int y){
  // 0 pixel <= x < 32 pixel
  if(x1 < 16 ){
    if(y < 8 || (y >= 16 && y < 24))
      x1 = x1;
    if((y >= 8 && y < 16) || (y >= 24 && y < 32)){
      x1 = x1 + 16;
    }
    return x1;
  }
  if(x1 >= 16 && x1 < 32){
    if(y < 8 || (y >= 16 && y < 24))
      x1 = x1 + 0x10;
    if((y >= 8 && y < 16) || (y >= 24 && y < 32))
      x1 = x1 + 0x20;
    return x1;
  }

  // 32 pixel <= x < 64 pixel
  if(x1 < 48 ){
    if(y < 8 || (y >= 16 && y < 24))
      x1 = x1 + 32;
    if((y >= 8 && y < 16) || (y >= 24 && y < 32)){
      x1 = x1 + 0x30;
    }
    return x1;
  }
  if(x1 >= 48 && x1 < 64){
    if(y < 8 || (y >= 16 && y < 24))
      x1 = x1 + 0x30;
    if((y >= 8 && y < 16) || (y >= 24 && y < 32))
      x1 = x1 + 0x40;
    return x1;
  }
  return x1;
}

// Remap Y coordinate for one row panel led
int remap_y(int yr){
  if(yr < 16)
    yr = yr & 0x7;
  if(yr >= 16 && yr < 32)
    yr = (yr & 0x7) + 8;
  return yr;
}

/*----------------------------- Wifi Configuration -------------------------------*/

const char *wifi_ssid = "Sanbroz";
const char *wifi_pass = "+919814318050";

/*----------------------------- RTC and NTP -------------------------------*/

I2C_BM8563 rtc(I2C_BM8563_DEFAULT_ADDRESS, Wire1);
const char* ntpServer         = "time.cloudflare.com";
const char* ntpLastUpdate     = "/ntp_last_update.txt";

// NTP Clock Offset / Timezone
#define CLOCK_GMT_OFFSET 1

/*-------------------------- HUB75E DMA Setup -----------------------------*/
// Physical panel is 32x32, but logically treated as 64x16 due to ICN5020B chip configuration
#define PANEL_RES_X 64      // Logical width (4 ICN5020B chips * 16 outputs each)
#define PANEL_RES_Y 16      // Logical height
#define PANEL_CHAIN 1       // Total number of panels chained one to another

// Physical dimensions for remapping
#define PHYSICAL_WIDTH 32
#define PHYSICAL_HEIGHT 32


#if defined(WF1)

HUB75_I2S_CFG::i2s_pins _pins_x1 = {WF1_R1_PIN, WF1_G1_PIN, WF1_B1_PIN, WF1_R2_PIN, WF1_G2_PIN, WF1_B2_PIN, WF1_A_PIN, WF1_B_PIN, WF1_C_PIN, WF1_D_PIN, WF1_E_PIN, WF1_LAT_PIN, WF1_OE_PIN, WF1_CLK_PIN};

#else

HUB75_I2S_CFG::i2s_pins _pins_x1 = {WF2_X1_R1_PIN, WF2_X1_G1_PIN, WF2_X1_B1_PIN, WF2_X1_R2_PIN, WF2_X1_G2_PIN, WF2_X1_B2_PIN, WF2_A_PIN, WF2_B_PIN, WF2_C_PIN, WF2_D_PIN, WF2_X1_E_PIN, WF2_LAT_PIN, WF2_OE_PIN, WF2_CLK_PIN};
HUB75_I2S_CFG::i2s_pins _pins_x2 = {WF2_X2_R1_PIN, WF2_X2_G1_PIN, WF2_X2_B1_PIN, WF2_X2_R2_PIN, WF2_X2_G2_PIN, WF2_X2_B2_PIN, WF2_A_PIN, WF2_B_PIN, WF2_C_PIN, WF2_D_PIN, WF2_X2_E_PIN, WF2_LAT_PIN, WF2_OE_PIN, WF2_CLK_PIN};

#endif


/*-------------------------- Class Instances ------------------------------*/
// Routing in the root page and webcamview.html natively uses the request
// handlers of the ESP32 WebServer class, so it explicitly instantiates the
// ESP32 WebServer.
WebServer           webServer;
WiFiMulti           wifiMulti;
ESP32Time           esp32rtc;  // offset in seconds GMT+1
MatrixPanel_I2S_DMA *dma_display = nullptr;

// INSTANTIATE A Button OBJECT FROM THE Bounce2 NAMESPACE
Bounce2::Button button = Bounce2::Button();

// ROS Task management
TaskHandle_t Task1;
TaskHandle_t Task2;

#include "led_pwm_handler.h"

RTC_DATA_ATTR int bootCount = 0;

volatile bool buttonPressed = false;

IRAM_ATTR void toggleButtonPressed() {
  // This function will be called when the interrupt occurs on pin PUSH_BUTTON_PIN
  buttonPressed = true;
  ESP_LOGI("toggleButtonPressed", "Interrupt Triggered.");

   esp_deep_sleep_start();      // Sleep for e.g. 30 minutes
  // Do something here
}



/*
Method to print the reason by which ESP32
has been awaken from sleep
*/
void print_wakeup_reason(){
  esp_sleep_wakeup_cause_t wakeup_reason;

  wakeup_reason = esp_sleep_get_wakeup_cause();

  switch(wakeup_reason)
  {
    case ESP_SLEEP_WAKEUP_EXT0 : Serial.println("Wakeup caused by external signal using RTC_IO"); break;
    case ESP_SLEEP_WAKEUP_EXT1 : Serial.println("Wakeup caused by external signal using RTC_CNTL"); break;
    case ESP_SLEEP_WAKEUP_TIMER : Serial.println("Wakeup caused by timer"); break;
    case ESP_SLEEP_WAKEUP_TOUCHPAD : Serial.println("Wakeup caused by touchpad"); break;
    case ESP_SLEEP_WAKEUP_ULP : Serial.println("Wakeup caused by ULP program"); break;
    default : Serial.printf("Wakeup was not caused by deep sleep: %d\n",wakeup_reason); break;
  }
}


// Function that gets current epoch time
unsigned long getEpochTime() {
  time_t now;
  struct tm timeinfo;
  if (!getLocalTime(&timeinfo)) {
    //Serial.println("Failed to obtain time");
    return(0);
  }
  time(&now);
  return now;
}

//
// Arduino Setup Task
//
void setup() {

  // Init USB CDC for ESP32-S2/S3
  #if SOC_USB_SERIAL_JTAG_SUPPORTED
  USB.begin();
  #endif

  // Init Serial
  // Standard Serial will work for both USB CDC and UART depending on board configuration
  Serial.begin(115200);

  /*-------------------- START THE HUB75E DISPLAY --------------------*/
    
    // Module configuration - Based on working ICN5020B example
    HUB75_I2S_CFG mxconfig(
      PANEL_RES_X,   // logical width (64)
      PANEL_RES_Y,   // logical height (16)
      PANEL_CHAIN,   // Chain length
      _pins_x1       // pin mapping for port X1
    );

    // Configuration matching the working example
    mxconfig.i2sspeed = HUB75_I2S_CFG::HZ_10M;  // Use 10MHz like working example
    mxconfig.latch_blanking = 1;                 // Use 1 like working example
    mxconfig.clkphase = false;                   // Explicitly set like working example
    //mxconfig.driver = HUB75_I2S_CFG::ICN2038S; // Uncomment if needed for ICN chips
    //mxconfig.double_buff = false;
    //mxconfig.min_refresh_rate = 30;


    // Display Setup
    dma_display = new MatrixPanel_I2S_DMA(mxconfig);
    dma_display->begin();
    dma_display->setBrightness8(128); //0-255
    dma_display->clearScreen();

    // COMMENTED OUT: Startup color animations
    /*
    dma_display->fillScreenRGB888(255,0,0);
    delay(1000);
    dma_display->fillScreenRGB888(0,255,0);
    delay(1000);
    dma_display->fillScreenRGB888(0,0,255);
    delay(1000);
    */

    // Simple startup message
    dma_display->setTextColor(dma_display->color565(255, 255, 255));
    dma_display->setCursor(2, 12);
    dma_display->print("Connecting...");


  /*-------------------- START THE NETWORKING --------------------*/
  WiFi.mode(WIFI_STA);
  wifiMulti.addAP(wifi_ssid, wifi_pass); // configure in the *-config.h file

  // wait for WiFi connection
  Serial.print("Waiting for WiFi to connect...");
  while (wifiMulti.run() != WL_CONNECTED) {
    Serial.print(".");
  }
  Serial.println(" connected");
    

  /*-------------------- --------------- --------------------*/
  //Increment boot number and print it every reboot
  ++bootCount;
  Serial.println("Boot number: " + String(bootCount));

  //Print the wakeup reason for ESP32
  print_wakeup_reason();

  esp_sleep_wakeup_cause_t wakeup_reason = esp_sleep_get_wakeup_cause();

  // COMMENTED OUT: Wake up display messages
  /*
  if ( wakeup_reason == ESP_SLEEP_WAKEUP_EXT0)
  {
    dma_display->setCursor(3,6);
    dma_display->print("Wake up!");
    delay(1000);
  }
  else
  {
    dma_display->print("Starting.");
  }
  */


  /*
    We set our ESP32 to wake up for an external trigger.
    There are two types for ESP32, ext0 and ext1 .
  */
  esp_sleep_enable_ext0_wakeup((gpio_num_t)PUSH_BUTTON_PIN, 0); //1 = High, 0 = Low  

  /*-------------------- --------------- --------------------*/
  // BUTTON SETUP 
  button.attach( PUSH_BUTTON_PIN, INPUT ); // USE EXTERNAL PULL-UP
  button.interval(5);   // DEBOUNCE INTERVAL IN MILLISECONDS
  button.setPressedState(LOW); // INDICATE THAT THE LOW STATE CORRESPONDS TO PHYSICALLY PRESSING THE BUTTON


  /*-------------------- LEDC Controller --------------------*/
    // Prepare and then apply the LEDC PWM timer configuration
    ledc_timer_config_t ledc_timer = {
        .speed_mode       = LEDC_LOW_SPEED_MODE,
        .duty_resolution  = LEDC_TIMER_13_BIT ,
        .timer_num        = LEDC_TIMER_0,
        .freq_hz          = 4000,  // Set output frequency at 4 kHz
        .clk_cfg          = LEDC_AUTO_CLK
    };
    ESP_ERROR_CHECK(ledc_timer_config(&ledc_timer));

    // Prepare and then apply the LEDC PWM channel configuration
    ledc_channel_config_t ledc_channel = {
        .gpio_num       = RUN_LED_PIN,
        .speed_mode     = LEDC_LOW_SPEED_MODE,
        .channel        = LEDC_CHANNEL_0,
        .intr_type      = LEDC_INTR_DISABLE,
        .timer_sel      = LEDC_TIMER_0,
        .duty           = 0, // Set duty to 0%
        .hpoint         = 0
    };
    ESP_ERROR_CHECK(ledc_channel_config(&ledc_channel));  


    // Start fading that LED
    xTaskCreatePinnedToCore(
      ledFadeTask,            /* Task function. */
      "ledFadeTask",                 /* name of task. */
      1000,                    /* Stack size of task */
      NULL,                     /* parameter of the task */
      1,                        /* priority of the task */
      &Task1,                   /* Task handle to keep track of created task */
      0);                       /* Core */   
    

  /*-------------------- INIT LITTLE FS --------------------*/
  if(!LittleFS.begin(FORMAT_LITTLEFS_IF_FAILED)){
      Serial.println("LittleFS Mount Failed");
      return;
  }
  listDir(LittleFS, "/", 1);    
 
  /*-------------------- --------------- --------------------*/
  // Init I2C for RTC
  Wire1.begin(BM8563_I2C_SDA, BM8563_I2C_SCL);
  rtc.begin();

  // Get RTC date and time
  I2C_BM8563_DateTypeDef rtcDate;
  I2C_BM8563_TimeTypeDef rtcTime;
  rtc.getDate(&rtcDate);
  rtc.getTime(&rtcTime);
  
  time_t ntp_last_update_ts = 0;
  File file = fs.open(ntpLastUpdate, FILE_READ, true);
  if(!file) {
      Serial.println("failed to open file for reading");
  } else  {
      file.read( (uint8_t*) &ntp_last_update_ts, sizeof(ntp_last_update_ts));          
      Serial.print("Epoch read from file: ");
      Serial.println(ntp_last_update_ts);
      file.close();      
  }

  // Current RTC
  std::tm curr_rtc_tm = make_tm(rtcDate.year, rtcDate.month, rtcDate.date);    // April 2nd, 2012
  time_t  curr_rtc_ts = std::mktime(&curr_rtc_tm);

  if ( std::abs( (long int) (curr_rtc_ts - ntp_last_update_ts)) > (60*60*24*30) && (bootCount == 0))
  {
      ESP_LOGI("need_ntp_update", "Longer than 30 days since last NTP update. Performing check.");    
  
      Serial.println("Updating RTC from Internet NTP.");  

      // Set ntp time to local
      configTime(CLOCK_GMT_OFFSET * 3600, 0, ntpServer);

      // Get local time
      struct tm timeInfo;
      if (getLocalTime(&timeInfo)) {
        // Set RTC time
        I2C_BM8563_TimeTypeDef timeStruct;
        timeStruct.hours   = timeInfo.tm_hour;
        timeStruct.minutes = timeInfo.tm_min;
        timeStruct.seconds = timeInfo.tm_sec;
        rtc.setTime(&timeStruct);

        // Set RTC Date
        I2C_BM8563_DateTypeDef dateStruct;
        dateStruct.weekDay = timeInfo.tm_wday;
        dateStruct.month   = timeInfo.tm_mon + 1;
        dateStruct.date    = timeInfo.tm_mday;
        dateStruct.year    = timeInfo.tm_year + 1900;
        rtc.setDate(&dateStruct);
    }

      ntp_last_update_ts = getEpochTime();
      File file = fs.open(ntpLastUpdate, FILE_WRITE);
      if(!file) {
          Serial.println("- failed to open file for writing");
      } else  {
          file.write( (uint8_t*) &ntp_last_update_ts, sizeof(ntp_last_update_ts));          
          file.close();      
          Serial.print("Wrote epoc time of: "); Serial.println(ntp_last_update_ts, DEC);            
      }

  } else {

    esp32rtc.setTime(rtcTime.seconds, rtcTime.minutes, rtcTime.hours, rtcDate.date, rtcDate.month, rtcDate.year);  // 17th Jan 2021 15:24:30
    Serial.println("Have a valid year on the external RTC. Updating ESP32 RTC to:");    
    Serial.println(esp32rtc.getTime("%A, %B %d %Y %H:%M:%S"));   // (String) returns time with specified format 
  }

   /*-------------------- --------------- --------------------*/

    webServer.on("/", []() {
      webServer.send(200, "text/plain", "Hi! I am here.");
    });

    ElegantOTA.begin(&webServer);    // Start ElegantOTA
    webServer.begin();
    Serial.println("OTA HTTP server started");

    /*-------------------- --------------- --------------------*/
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());  

    delay(1000);

    // COMMENTED OUT: IP address display
    /*
    dma_display->clearScreen();
    dma_display->setCursor(0,0);
    dma_display->print(WiFi.localIP());
    dma_display->clearScreen();
    delay(3000);
    */

    // Clear screen and prepare for time display
    dma_display->clearScreen();

}

// Diagnostic variables
static int diagnostic_mode = 0;
static bool diagnostic_running = false;

void loop()
{

    // YOU MUST CALL THIS EVERY LOOP
    button.update();

    if ( button.pressed() ) {

     toggleButtonPressed();

    }

    webServer.handleClient();

    // Run diagnostic sequence once, then wait
    if (!diagnostic_running) {
      diagnostic_running = true;

      // Mode 0: SLOW Horizontal fill for monitoring
      Serial.println("=== Starting SLOW horizontal fill diagnostic ===");
      Serial.println("=== Please report the flow pattern using the grid below ===");
      Serial.println("");
      Serial.println("32x32 Display Grid (8 sections: 4 rows x 2 columns):");
      Serial.println("┌─────────────────┬─────────────────┐");
      Serial.println("│  Section A1     │  Section A2     │ Row 1 (Y: 0-7)");
      Serial.println("│  (X:0-15,Y:0-7) │ (X:16-31,Y:0-7) │");
      Serial.println("├─────────────────┼─────────────────┤");
      Serial.println("│  Section B1     │  Section B2     │ Row 2 (Y: 8-15)");
      Serial.println("│ (X:0-15,Y:8-15) │(X:16-31,Y:8-15) │");
      Serial.println("├─────────────────┼─────────────────┤");
      Serial.println("│  Section C1     │  Section C2     │ Row 3 (Y: 16-23)");
      Serial.println("│(X:0-15,Y:16-23) │(X:16-31,Y:16-23)│");
      Serial.println("├─────────────────┼─────────────────┤");
      Serial.println("│  Section D1     │  Section D2     │ Row 4 (Y: 24-31)");
      Serial.println("│(X:0-15,Y:24-31) │(X:16-31,Y:24-31)│");
      Serial.println("└─────────────────┴─────────────────┘");
      Serial.println("");
      Serial.println("Watch the LED flow and report like: 'A1->B1->C1->D1->A2->B2->C2->D2'");
      Serial.println("Starting in 3 seconds...");
      delay(3000);

      dma_display->clearScreen();
      uint16_t color1 = dma_display->color565(255, 255, 255); // Bright white for visibility

      for (int y = 0; y < PHYSICAL_HEIGHT; y++) {
        for (int x = 0; x < PHYSICAL_WIDTH; x++) {
          // Keep all previous pixels lit, add new pixel
          dma_display->drawPixel(remap_x(x, y), remap_y(y), color1);

          // Determine which section this pixel is in
          String section = "";
          if (y >= 0 && y <= 7) {
            section = (x <= 15) ? "A1" : "A2";
          } else if (y >= 8 && y <= 15) {
            section = (x <= 15) ? "B1" : "B2";
          } else if (y >= 16 && y <= 23) {
            section = (x <= 15) ? "C1" : "C2";
          } else if (y >= 24 && y <= 31) {
            section = (x <= 15) ? "D1" : "D2";
          }

          Serial.printf("Pixel %03d: Physical(%02d,%02d) -> Logical(%02d,%02d) -> Section %s\n",
                       (y * PHYSICAL_WIDTH + x + 1), x, y, remap_x(x, y), remap_y(y), section.c_str());
          delay(350); // 500ms delay (double speed)
        }
      }

      Serial.println("\n==== Horizontal fill complete ====");
      Serial.println("Please report the flow pattern you observed.");
      Serial.println("Example: 'A1->B1->C1->D1->A2->B2->C2->D2' or whatever you saw");
      Serial.println("Restarting in 10 seconds...");
      delay(10000);
      diagnostic_running = false; // Restart the cycle
    }

} // loop() 